"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Volume2, VolumeX, Loader2, Check } from 'lucide-react';
import { VOICE_METADATA, type TTSVoiceName } from '@/lib/tts-voices';
import { useAudio } from '@/hooks/use-audio';
import { cn } from '@/lib/utils';

interface VoiceSelectionGridProps {
  selectedVoice: TTSVoiceName;
  onVoiceSelect: (voice: TTSVoiceName) => void;
  title: string;
  description: string;
}

export function VoiceSelectionGrid({
  selectedVoice,
  onVoiceSelect,
  title,
  description
}: VoiceSelectionGridProps) {
  const { audioState, playAudioUrl, stopAudio } = useAudio();
  const [playingVoice, setPlayingVoice] = useState<TTSVoiceName | null>(null);

  // Group voices by gender
  const maleVoices = Object.values(VOICE_METADATA).filter(voice => voice.gender === 'male');
  const femaleVoices = Object.values(VOICE_METADATA).filter(voice => voice.gender === 'female');

  const handlePreviewClick = async (voice: TTSVoiceName, previewUrl: string) => {
    if (playingVoice === voice && audioState.isPlaying) {
      // Stop if currently playing this voice
      stopAudio();
      setPlayingVoice(null);
    } else {
      // Stop any current audio and play new one
      stopAudio();
      setPlayingVoice(voice);
      
      try {
        await playAudioUrl(previewUrl);
      } catch (error) {
        console.error('Error playing preview:', error);
        setPlayingVoice(null);
      }
    }
  };

  const isVoicePlaying = (voice: TTSVoiceName) => {
    return playingVoice === voice && audioState.isPlaying;
  };

  const isVoiceLoading = (voice: TTSVoiceName) => {
    return playingVoice === voice && audioState.isLoading;
  };

  const VoiceCard = ({ voice }: { voice: typeof VOICE_METADATA[TTSVoiceName] }) => {
    const isSelected = selectedVoice === voice.name;
    const isPlaying = isVoicePlaying(voice.name);
    const isLoading = isVoiceLoading(voice.name);

    return (
      <Card 
        className={cn(
          "cursor-pointer transition-all duration-200 hover:shadow-md",
          isSelected && "ring-2 ring-primary border-primary"
        )}
        onClick={() => onVoiceSelect(voice.name)}
      >
        <CardContent className="p-4">
          <div className="flex items-start justify-between mb-2">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-semibold text-sm">{voice.name}</h3>
                {isSelected && <Check className="h-4 w-4 text-primary" />}
              </div>
              <div className="flex flex-wrap gap-1 mb-2">
                <Badge variant="secondary" className="text-xs">
                  {voice.gender}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {voice.accent}
                </Badge>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 ml-2"
              onClick={(e) => {
                e.stopPropagation();
                handlePreviewClick(voice.name, voice.preview_url);
              }}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : isPlaying ? (
                <VolumeX className="h-4 w-4" />
              ) : (
                <Volume2 className="h-4 w-4" />
              )}
            </Button>
          </div>
          <p className="text-xs text-muted-foreground mb-1">
            {voice.description} • {voice.age}
          </p>
          <p className="text-xs text-muted-foreground">
            {voice.use_case}
          </p>
        </CardContent>
      </Card>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {title}
        </CardTitle>
        <CardDescription>
          {description}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Male Voices Section */}
        <div>
          <h4 className="font-medium text-sm mb-3 text-muted-foreground">
            Male Voices ({maleVoices.length})
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {maleVoices.map((voice) => (
              <VoiceCard key={voice.name} voice={voice} />
            ))}
          </div>
        </div>

        {/* Female Voices Section */}
        <div>
          <h4 className="font-medium text-sm mb-3 text-muted-foreground">
            Female Voices ({femaleVoices.length})
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {femaleVoices.map((voice) => (
              <VoiceCard key={voice.name} voice={voice} />
            ))}
          </div>
        </div>

        <div className="text-xs text-muted-foreground pt-4 border-t">
          <p>
            <strong>Tip:</strong> Click the speaker icon to preview each voice. 
            Select a voice by clicking on its card.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
