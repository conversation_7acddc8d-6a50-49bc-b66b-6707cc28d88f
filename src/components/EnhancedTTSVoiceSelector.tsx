"use client";

import React from 'react';
import { VoiceSelectionGrid } from './VoiceSelectionGrid';
import { type TTSVoiceName } from '@/lib/tts-voices';
import { useSubscription } from '@/hooks/use-subscription';

interface EnhancedTTSVoiceSelectorProps {
  questionVoice: TTSVoiceName;
  answerVoice: TTSVoiceName;
  onQuestionVoiceChange: (voice: TTSVoiceName) => void;
  onAnswerVoiceChange: (voice: TTSVoiceName) => void;
}

export function EnhancedTTSVoiceSelector({
  questionVoice,
  answerVoice,
  onQuestionVoiceChange,
  onAnswerVoiceChange
}: EnhancedTTSVoiceSelectorProps) {
  const { isPro } = useSubscription();

  return (
    <div className="space-y-6">
      {/* Question Voice Selection */}
      <VoiceSelectionGrid
        selectedVoice={questionVoice}
        onVoiceSelect={onQuestionVoiceChange}
        title="Question Voice"
        description={
          isPro
            ? "Choose the AI voice that will read your flashcard questions. Click any voice card to select it and use the speaker icon to preview."
            : "Choose the AI voice that will read your flashcard questions. You'll need to provide your own ElevenLabs API key to use these voices."
        }
      />

      {/* Answer Voice Selection */}
      <VoiceSelectionGrid
        selectedVoice={answerVoice}
        onVoiceSelect={onAnswerVoiceChange}
        title="Answer Voice"
        description={
          isPro
            ? "Choose the AI voice that will read your flashcard answers. You can use the same voice as questions or pick a different one for variety."
            : "Choose the AI voice that will read your flashcard answers. You'll need to provide your own ElevenLabs API key to use these voices."
        }
      />

      <div className="text-sm text-muted-foreground bg-muted/50 p-4 rounded-lg">
        <h4 className="font-medium mb-2">Voice Selection Tips:</h4>
        <ul className="space-y-1 text-xs">
          <li>• <strong>Educational Focus:</strong> All voices are optimized for clear pronunciation and learning</li>
          <li>• <strong>Accent Variety:</strong> Choose from American and British accents for different learning preferences</li>
          <li>• <strong>Gender Balance:</strong> Equal selection of male and female voices for personalization</li>
          <li>• <strong>Preview First:</strong> Listen to voice samples before making your selection</li>
          <li>• <strong>Mix & Match:</strong> Use different voices for questions and answers to improve focus</li>
        </ul>
      </div>
    </div>
  );
}
