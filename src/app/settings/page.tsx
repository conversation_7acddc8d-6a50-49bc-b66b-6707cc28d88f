"use client";

import { useEffect, useState } from 'react';
import { constructMetadata } from '../metadata';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth-context';
import { But<PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Save, ChevronLeft, Database } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useSettingsService } from '@/lib/settings-service';
import type { UserSettings } from '@/types';
import { EnhancedTTSVoiceSelector } from '@/components/EnhancedTTSVoiceSelector';
import { ElevenLabsApiKeySettings } from '@/components/ElevenLabsApiKeySettings';
import { DEFAULT_TTS_VOICES, getSafeVoiceName, type TTSVoiceName } from '@/lib/tts-voices';
import { useSubscription } from '@/hooks/use-subscription';
import { Switch } from '@/components/ui/switch';
import { getCookieConsent, setCookieConsent } from '@/lib/cookie-consent';
import Link from 'next/link';
import { logger } from '@/lib/logger';

// Use the same values for both marks and lines
const DETAIL_LEVEL_POSITIONS = [
  { value: 2, label: 'Concise' },    // Slightly inset from 0
  { value: 50, label: 'Balanced' },
  { value: 98, label: 'Detailed' }   // Slightly inset from 100
] as const;

// Keep the actual snap points at 0, 50, 100 for the slider functionality
const DETAIL_LEVELS = {
  CONCISE: 0,
  BALANCED: 50,
  DETAILED: 100
} as const;

const getDetailLevelLabel = (value: number): string => {
  if (value <= 25) return 'Concise';
  if (value <= 75) return 'Balanced';
  return 'Detailed';
};

const snapToDetailLevel = (value: number): number => {
  if (value <= 25) return DETAIL_LEVELS.CONCISE;
  if (value <= 75) return DETAIL_LEVELS.BALANCED;
  return DETAIL_LEVELS.DETAILED;
};

export default function SettingsPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  const { getUserSettings, updateUserSettings, DEFAULT_SETTINGS } = useSettingsService();
  const { requiresOwnApiKey, subscriptionsEnabled } = useSubscription();
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Settings state
  const [answerDetail, setAnswerDetail] = useState(DEFAULT_SETTINGS.answerDetail);
  const [cardRange, setCardRange] = useState([
    DEFAULT_SETTINGS.cardRange.min,
    DEFAULT_SETTINGS.cardRange.max
  ]);
  const [ttsVoices, setTtsVoices] = useState<{
    questionVoice: TTSVoiceName;
    answerVoice: TTSVoiceName;
  }>({
    questionVoice: DEFAULT_TTS_VOICES.questionVoice,
    answerVoice: DEFAULT_TTS_VOICES.answerVoice
  });
  const [elevenLabsApiKey, setElevenLabsApiKey] = useState('');
  const [analyticsEnabled, setAnalyticsEnabled] = useState(true);

  useEffect(() => {
    // Load cookie consent state
    const consent = getCookieConsent();
    setAnalyticsEnabled(consent.analytics);
  }, []);

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!loading && !user) {
      router.push('/auth/signin?redirect=/settings');
      return;
    }

    // Load user settings
    if (user) {
      loadUserSettings();
    }
  }, [user, loading]);

  const loadUserSettings = async () => {
    if (!user) return;

    try {
      const settings = await getUserSettings(user.id);
      setAnswerDetail(settings.answerDetail);
      setCardRange([settings.cardRange.min, settings.cardRange.max]);
      setTtsVoices({
        questionVoice: getSafeVoiceName(settings.ttsVoices?.questionVoice, DEFAULT_TTS_VOICES.questionVoice),
        answerVoice: getSafeVoiceName(settings.ttsVoices?.answerVoice, DEFAULT_TTS_VOICES.answerVoice)
      });

      // Get API key from server-side API if user requires their own key
      if (requiresOwnApiKey) {
        try {
          const apiResponse = await fetch(`/api/settings?userId=${user.id}`);
          if (apiResponse.ok) {
            const apiData = await apiResponse.json();
            setElevenLabsApiKey(apiData.settings?.elevenLabsApiKey || '');
          }
        } catch (error) {
          logger.error('Error fetching API key:', error);
        }
      }
    } catch (error) {
      logger.error('Error loading settings:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load settings. Using default values.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!user) return;

    setIsSaving(true);
    try {
      const settingsUpdate: any = {
        answerDetail,
        cardRange: {
          min: cardRange[0],
          max: cardRange[1]
        },
        ttsVoices
      };

      // Include API key if user requires their own key (even if empty for deletion)
      if (requiresOwnApiKey) {
        settingsUpdate.elevenLabsApiKey = elevenLabsApiKey.trim();
        logger.log('[Settings Page] Including API key in update:', {
          originalApiKey: elevenLabsApiKey,
          trimmedApiKey: elevenLabsApiKey.trim(),
          isEmpty: elevenLabsApiKey.trim() === '',
          length: elevenLabsApiKey.trim().length
        });
      } else {
        logger.log('[Settings Page] User does not require own API key, skipping');
      }

      logger.log('[Settings Page] Final settings update object:', {
        ...settingsUpdate,
        elevenLabsApiKey: settingsUpdate.elevenLabsApiKey ? '[REDACTED]' : settingsUpdate.elevenLabsApiKey
      });

      const result = await updateUserSettings(user.id, settingsUpdate);

      if (result.success) {
        toast({
          title: "Settings saved",
          description: "Your preferences have been updated successfully.",
        });
        router.push('/');
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      logger.error('Error saving settings:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to save settings. Please try again.",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleAnalyticsToggle = (enabled: boolean) => {
    setAnalyticsEnabled(enabled);
    setCookieConsent({ analytics: enabled });

    toast({
      title: enabled ? "Analytics enabled" : "Analytics disabled",
      description: enabled
        ? "We'll collect anonymous usage data to improve the app."
        : "We won't collect analytics data from your usage.",
    });
  };

  if (loading || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen pwa-safe-all">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <main className="container px-4 py-8 mx-auto max-w-2xl pwa-safe-all">
      <div className="mb-8 flex items-center">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/" className="flex items-center">
            <ChevronLeft className="mr-1 h-4 w-4" />
            Back to Home
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Settings</CardTitle>
          <CardDescription>
            Customize your flashcard generation preferences
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Answer Detail Slider */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Label htmlFor="answer-detail">Answer Detail Level</Label>
              <span className="text-sm text-muted-foreground">
                {getDetailLevelLabel(answerDetail)}
              </span>
            </div>
            <div className="pt-2 pb-8 relative mx-1">
              {/* Marks for snap points - use inset values for visual markers */}
              <div className="absolute left-0 right-0 top-[15px] flex justify-between pointer-events-none">
                {DETAIL_LEVEL_POSITIONS.map(mark => (
                  <div
                    key={mark.value}
                    className="h-2 w-0.5 bg-muted-foreground/30"
                    style={{
                      position: 'absolute',
                      left: `${mark.value}%`,
                      transform: 'translateX(-50%)'
                    }}
                  />
                ))}
              </div>
              <Slider
                id="answer-detail"
                min={0}
                max={100}
                step={50}
                value={[answerDetail]}
                onValueChange={(value) => setAnswerDetail(snapToDetailLevel(value[0]))}
                className="w-full"
              />
              {/* Labels below slider - use same inset values */}
              <div className="absolute left-0 right-0 top-6 flex justify-between">
                {DETAIL_LEVEL_POSITIONS.map(mark => (
                  <div
                    key={mark.value}
                    className="flex flex-col items-center"
                    style={{
                      position: 'absolute',
                      left: `${mark.value}%`,
                      transform: 'translateX(-50%)'
                    }}
                  >
                    <span className="text-xs text-muted-foreground mt-2">
                      {mark.label}
                    </span>
                  </div>
                ))}
              </div>
            </div>
            <p className="text-sm text-muted-foreground">
              Adjust how detailed the answers should be on flashcards.
              More detailed answers provide more context but may take longer to review.
            </p>
          </div>

          {/* Card Count Range Slider */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Label htmlFor="card-range">Number of Cards</Label>
              <span className="text-sm text-muted-foreground">
                {cardRange[0]} - {cardRange[1]} cards
              </span>
            </div>
            <Slider
              id="card-range"
              min={3}
              max={100}
              step={1}
              value={cardRange}
              onValueChange={(value) => {
                // Ensure min value is always less than max value
                if (value[0] < value[1]) {
                  setCardRange(value);
                }
              }}
              className="w-full"
            />
            <p className="text-sm text-muted-foreground">
              Set the minimum and maximum number of flashcards to generate from each document.
              The AI will create cards within this range based on content complexity.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Privacy & Analytics Settings */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Privacy & Analytics</CardTitle>
          <CardDescription>
            Control how we collect and use your data
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Analytics Toggle */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="analytics-toggle">Analytics & Usage Data</Label>
              <p className="text-sm text-muted-foreground">
                Help us improve the app by sharing anonymous usage statistics.
                This includes page views, feature usage, and performance data.
              </p>
            </div>
            <Switch
              id="analytics-toggle"
              checked={analyticsEnabled}
              onCheckedChange={handleAnalyticsToggle}
            />
          </div>

          <div className="text-xs text-muted-foreground border-t pt-4">
            <p>
              <strong>What we collect when enabled:</strong> Page views, button clicks,
              feature usage, and performance metrics. No personal information is included.
            </p>
            <p className="mt-2">
              <strong>Essential cookies:</strong> Always enabled for app functionality
              (login state, preferences, etc.)
            </p>
          </div>
        </CardContent>
      </Card>

      {/* ElevenLabs API Key Settings - only show for FREE/BASIC users */}
      {requiresOwnApiKey && (
        <div className="mt-6">
          <ElevenLabsApiKeySettings
            currentApiKey={elevenLabsApiKey}
            onApiKeyChange={setElevenLabsApiKey}
          />
        </div>
      )}

      {/* Enhanced TTS Voice Settings */}
      <div className="mt-6">
        <EnhancedTTSVoiceSelector
          questionVoice={ttsVoices.questionVoice}
          answerVoice={ttsVoices.answerVoice}
          onQuestionVoiceChange={(voice) => setTtsVoices(prev => ({ ...prev, questionVoice: voice }))}
          onAnswerVoiceChange={(voice) => setTtsVoices(prev => ({ ...prev, answerVoice: voice }))}
        />
      </div>

      <div className="flex justify-end mt-6">
        <Button onClick={handleSave} disabled={isSaving}>
          {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          <Save className="mr-2 h-4 w-4" />
          {isSaving ? 'Saving...' : 'Save Settings'}
        </Button>
      </div>

      {/* Data Management Link */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Data Management</CardTitle>
          <CardDescription>
            Export your data, manage your account, and access privacy controls
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button asChild className="w-full sm:w-auto">
            <Link href="/data-management" className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              Manage Your Data
            </Link>
          </Button>
          <p className="text-sm text-muted-foreground mt-3">
            Access data export options, account deletion, and privacy settings on the dedicated data management page.
          </p>
        </CardContent>
      </Card>
    </main>
  );
}