"use client";

import React, { useState, useEffect, useRef } from 'react';
import { FileUploadForm } from '@/components/FileUploadForm';
import { TopicInputForm } from '@/components/TopicInputForm';
import { FlashcardViewer } from '@/components/FlashcardViewer';
import { ExportButton } from '@/components/ExportButton';
import type { FlashcardData, GenerationMode } from '@/types';

import { Button } from '@/components/ui/button';
import Image from 'next/image';
import { useAuth } from '@/lib/auth-context';
import { useFlashcardService } from '@/lib/flashcard-service';
import { useSettingsService } from '@/lib/settings-service';
import Link from 'next/link';
import { LogOut, User, History, Settings } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { logger } from '@/lib/logger';
import { Footer } from '@/components/Footer';
import { useSubscriptionSync } from '@/hooks/use-subscription-sync';

export default function Home() {
  const [flashcards, setFlashcards] = useState<FlashcardData[] | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const [hasHistory, setHasHistory] = useState(false);
  const [flashcardTitle, setFlashcardTitle] = useState<string>('');
  const [userSettings, setUserSettings] = useState<{ answerDetail: number; cardRange: { min: number; max: number } } | null>(null);
  const [generationMode, setGenerationMode] = useState<GenerationMode>('document');
  const [videoAspectRatio, setVideoAspectRatio] = useState<string>('4/3');
  const historyCheckAttemptsRef = useRef(0);
  const historyCheckedRef = useRef(false);

  const { user, logout, loading } = useAuth();
  const { saveUserFlashcards, getUserSets } = useFlashcardService();
  const { getUserSettings, DEFAULT_SETTINGS } = useSettingsService();
  const { toast } = useToast();
  const { syncUserSubscription } = useSubscriptionSync();

  // Load user settings
  useEffect(() => {
    const loadSettings = async () => {
      if (!user) {
        setUserSettings(DEFAULT_SETTINGS);
        return;
      }
      try {
        const settings = await getUserSettings(user.id);
        setUserSettings(settings);
      } catch (error) {
        logger.error('Error loading settings:', error);
        setUserSettings(DEFAULT_SETTINGS);
      }
    };

    loadSettings();
  }, [user?.id, getUserSettings, DEFAULT_SETTINGS]);

  // Sync subscription status on homepage load
  useEffect(() => {
    const performSubscriptionSync = async () => {
      // Only run if user is authenticated and not loading
      if (!user || loading) {
        return;
      }

      try {
        const result = await syncUserSubscription({ source: 'homepage' });

        if (result.success && result.updated) {
          // Show toast notification if subscription was updated
          toast({
            title: "Subscription Updated",
            description: `Your subscription has been synchronized. You now have ${result.newTier} access.`,
          });
        } else if (result.success && !result.updated && !result.skipped) {
          logger.log('[SYNC] Subscription already in sync');
        } else if (result.skipped) {
          logger.log(`[SYNC] Subscription sync skipped: ${result.reason}`);
        } else if (!result.success && result.error) {
          logger.error('[SYNC] Subscription sync failed:', result.error);
          // Don't show error toast to user as this is a background operation
        }
      } catch (error) {
        logger.error('[SYNC] Unexpected error during subscription sync:', error);
      }
    };

    performSubscriptionSync();
  }, [user?.id, loading, syncUserSubscription, toast]);

  // Prevent default drag and drop behavior on the window
  useEffect(() => {
    const preventDefault = (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
    };

    window.addEventListener('dragover', preventDefault);
    window.addEventListener('drop', preventDefault);

    return () => {
      window.removeEventListener('dragover', preventDefault);
      window.removeEventListener('drop', preventDefault);
    };
  }, []);

  // Check if user has any flashcard sets in history
  useEffect(() => {
    const checkForHistory = async () => {
      // If we already found history or exceeded max attempts (3), don't check again
      if (hasHistory || historyCheckAttemptsRef.current >= 3 || historyCheckedRef.current) {
        return;
      }

      if (!user) {
        setHasHistory(false);
        return;
      }

      historyCheckAttemptsRef.current += 1;

      try {
        const sets = await getUserSets();
        const hasUserHistory = sets.length > 0;
        setHasHistory(hasUserHistory);

        // If we found history, or we've made enough attempts, mark as fully checked
        if (hasUserHistory || historyCheckAttemptsRef.current >= 3) {
          historyCheckedRef.current = true;
        }
      } catch (error) {
        logger.error('Error checking flashcard history:', error);
        // Count a failed attempt as a check
        if (historyCheckAttemptsRef.current >= 3) {
          historyCheckedRef.current = true;
        }
      }
    };

    // Only check if we're not loading authentication state and user is logged in
    if (!loading) {
      checkForHistory();
    }
  }, [user?.id, loading, getUserSets, hasHistory]);

  // Reset history check state when user changes
  useEffect(() => {
    historyCheckedRef.current = false;
    historyCheckAttemptsRef.current = 0;
    setHasHistory(false);
  }, [user?.id]); // Reset when user ID changes



  const handleFlashcardsGenerated = async (generatedFlashcards: FlashcardData[], title?: string) => {
    setFlashcards(generatedFlashcards);
    const newTitle = title ? title : `Flashcards - ${new Date().toLocaleString()}`;
    setFlashcardTitle(newTitle);

    // Automatically save to history if user is logged in and flashcards exist
    if (user && generatedFlashcards && generatedFlashcards.length > 0) {
      try {
        const result = await saveUserFlashcards(generatedFlashcards, newTitle);
        if (result.success) {
          setHasHistory(true);
          historyCheckedRef.current = true;
          toast({
            title: "Success!",
            description: "Flashcards saved to your history",
          });
        } else if (result.isDuplicate) {
          toast({
            title: "Already Saved",
            description: "This flashcard set has already been saved to your history."
          });
        } else {
          throw new Error('Failed to save flashcards');
        }
      } catch {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to save flashcards to history",
        });
      }
    }
  };

  const getDetailLevelText = (detail: number) => {
    if (detail < 33) return 'concise';
    if (detail < 66) return 'balanced';
    return 'detailed';
  };

  const resetApp = () => {
    setFlashcards(null);
    setIsLoading(false);
    setFlashcardTitle('');
  };

  const handleVideoLoadedMetadata = (event: React.SyntheticEvent<HTMLVideoElement>) => {
    const video = event.currentTarget;
    const aspectRatio = video.videoWidth / video.videoHeight;
    setVideoAspectRatio(`${video.videoWidth}/${video.videoHeight}`);
  };

  const handleLogout = async () => {
    try {
      await logout();
      toast({
        title: "Logged out",
        description: "You have been successfully logged out.",
      });
    } catch {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to log out",
      });
    }
  };

  return (
    <main
      className="flex flex-col items-center justify-center min-h-full p-4 sm:p-8 bg-background pwa-safe-all"
      onDragOver={(e) => e.preventDefault()}
      onDrop={(e) => e.preventDefault()}
    >
      <header className="mb-8 w-full max-w-2xl mx-auto overflow-x-hidden">
        {/* User navigation elements */}
        <div className="flex justify-end mb-4">
          {!loading && (
            <>
              {user ? (
                <div className="flex items-center gap-2">
                  {hasHistory && (
                    <Link href="/history">
                      <Button variant="outline" size="sm" className="mr-2 flex items-center gap-1">
                        <History className="h-4 w-4" />
                        <span className="hidden sm:inline">History</span>
                      </Button>
                    </Link>
                  )}
                  {process.env.NEXT_PUBLIC_ENABLE_SUBSCRIPTIONS === 'true' && (
                    <Link href="/subscription">
                      <Button variant="outline" size="sm" className="mr-2">
                        <span>Subscription</span>
                      </Button>
                    </Link>
                  )}
                  <Link href="/settings">
                    <Button
                      variant="outline"
                      size="sm"
                      className="mr-2 flex items-center gap-1"
                    >
                      <Settings className="h-4 w-4" />
                      <span className="hidden sm:inline">Settings</span>
                    </Button>
                  </Link>
                  <div className="flex items-center gap-2 text-sm bg-muted p-2 rounded-lg">
                    <User className="h-4 w-4" />
                    <span className="hidden sm:inline">{user.name || user.email}</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleLogout}
                    aria-label="Logout"
                  >
                    <LogOut className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <Link href="/auth/signin">
                  <Button variant="outline" size="sm">Sign in</Button>
                </Link>
              )}
            </>
          )}
        </div>

        {/* App title */}
        <div className="flex flex-col items-center justify-center text-center">
          <div className="flex items-center justify-center gap-3">
            <Image
              src="/flashcard_icon.png"
              alt="Flashcard AI Logo"
              width={48}
              height={48}
              className="text-primary"
            />
            <h1 className="text-2xl sm:text-4xl font-bold text-semibold">Flashcard AI</h1>
          </div>
          <p className="text-muted-foreground mt-2">
            Upload documents or enter any topic to generate AI-powered flashcards!
          </p>
          <p className="text-muted-foreground text-sm mt-1">
            Complete with natural AI voice synthesis for immersive audio learning.
          </p>
        </div>
      </header>

      {/* Mode Selection */}
      {!isLoading && !flashcards && (
        <div className="flex justify-center mb-6">
          <div className="bg-muted p-1 rounded-lg flex gap-1">
            <Button
              variant={generationMode === 'document' ? 'default' : 'ghost'}
              onClick={() => setGenerationMode('document')}
              className="px-4 py-2"
            >
              Upload Documents
            </Button>
            <Button
              variant={generationMode === 'topic' ? 'default' : 'ghost'}
              onClick={() => setGenerationMode('topic')}
              className="px-4 py-2"
            >
              Enter Topic
            </Button>
          </div>
        </div>
      )}

      <div className="w-full max-w-2xl mx-auto flex justify-center items-center">
        {isLoading && !flashcards && (
          <div className="flex flex-col items-center space-y-4 w-full">
            <video
              src="/card-writing-animation.mp4"
              autoPlay
              loop
              muted
              playsInline
              onLoadedMetadata={handleVideoLoadedMetadata}
              className="w-full max-w-md mx-auto rounded-lg shadow-lg object-contain bg-black"
              style={{ aspectRatio: videoAspectRatio }}
              aria-label="Generating animation"
            />
            <div className="space-y-2 text-center">
              <p className="text-lg text-primary animate-pulse">Generating your flashcards, please wait...</p>
              {userSettings && (
                <p className="text-sm text-muted-foreground">
                  Generating {userSettings.cardRange.min}-{userSettings.cardRange.max} cards with{' '}
                  {getDetailLevelText(userSettings.answerDetail)} answers
                </p>
              )}
            </div>
          </div>
        )}

        {!isLoading && !flashcards && (
          <div className="w-full flex justify-center">
            {generationMode === 'document' ? (
              <FileUploadForm
                onFlashcardsGenerated={handleFlashcardsGenerated}
                setIsLoading={setIsLoading}
              />
            ) : (
              <TopicInputForm
                onFlashcardsGenerated={handleFlashcardsGenerated}
                setIsLoading={setIsLoading}
              />
            )}
          </div>
        )}

        {!isLoading && flashcards && flashcards.length > 0 && (
          <div className="space-y-8 w-full">
            <h2 className="w-full min-w-0 truncate text-base sm:text-xl font-semibold text-center text-foreground">{flashcardTitle}</h2>
            <FlashcardViewer flashcards={flashcards} />
            <div className="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-4">
              <ExportButton flashcards={flashcards} title={flashcardTitle} />
              <button
                onClick={resetApp}
                className="text-sm text-primary hover:underline"
                aria-label="Generate new flashcards"
              >
                Generate new flashcards
              </button>
            </div>
          </div>
        )}
         {!isLoading && flashcards && flashcards.length === 0 && (
           <div className="text-center p-8 bg-card rounded-lg shadow-md w-full max-w-md mx-auto">
            <p className="text-xl text-card-foreground mb-4">No flashcards were generated.</p>
            <p className="text-muted-foreground mb-6">This might be due to the content of the files or an issue with processing. Please try different documents.</p>
            <Button onClick={resetApp} variant="outline">
              Upload Other Files
            </Button>
          </div>
        )}
      </div>
      <Footer />
    </main>
  );
}
